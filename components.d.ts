/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AnyMonthlyAverageIncomeCalculator: typeof import('./src/components/any-monthly-average-income-calculator/index.vue')['default']
    ExcelBatchCalculator: typeof import('./src/components/excel-batch-calculator/index.vue')['default']
    MonthlyAverageIncomeCalculator: typeof import('./src/components/monthly-average-income-calculator/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    TaxResultTable: typeof import('./src/components/tax-result-table/index.vue')['default']
    VAlert: typeof import('vuetify/components')['VAlert']
    VAlertTitle: typeof import('vuetify/components')['VAlertTitle']
    VApp: typeof import('vuetify/components')['VApp']
    VAppBar: typeof import('vuetify/components')['VAppBar']
    VAppBarTitle: typeof import('vuetify/components')['VAppBarTitle']
    VAvatar: typeof import('vuetify/components')['VAvatar']
    VBtn: typeof import('vuetify/components')['VBtn']
    VCard: typeof import('vuetify/components')['VCard']
    VCardSubtitle: typeof import('vuetify/components')['VCardSubtitle']
    VCardText: typeof import('vuetify/components')['VCardText']
    VCardTitle: typeof import('vuetify/components')['VCardTitle']
    VChip: typeof import('vuetify/components')['VChip']
    VCol: typeof import('vuetify/components')['VCol']
    VContainer: typeof import('vuetify/components')['VContainer']
    VDataTable: typeof import('vuetify/components')['VDataTable']
    VDivider: typeof import('vuetify/components')['VDivider']
    VExpansionPanel: typeof import('vuetify/components')['VExpansionPanel']
    VExpansionPanels: typeof import('vuetify/components')['VExpansionPanels']
    VExpansionPanelText: typeof import('vuetify/components')['VExpansionPanelText']
    VExpansionPanelTitle: typeof import('vuetify/components')['VExpansionPanelTitle']
    VFooter: typeof import('vuetify/components')['VFooter']
    VForm: typeof import('vuetify/components')['VForm']
    VIcon: typeof import('vuetify/components')['VIcon']
    VList: typeof import('vuetify/components')['VList']
    VListItem: typeof import('vuetify/components')['VListItem']
    VListItemSubtitle: typeof import('vuetify/components')['VListItemSubtitle']
    VListItemTitle: typeof import('vuetify/components')['VListItemTitle']
    VMain: typeof import('vuetify/components')['VMain']
    VProgressCircular: typeof import('vuetify/components')['VProgressCircular']
    VProgressLinear: typeof import('vuetify/components')['VProgressLinear']
    VResponsive: typeof import('vuetify/components')['VResponsive']
    VRow: typeof import('vuetify/components')['VRow']
    VSpacer: typeof import('vuetify/components')['VSpacer']
    VTab: typeof import('vuetify/components')['VTab']
    VTabs: typeof import('vuetify/components')['VTabs']
    VTabsWindow: typeof import('vuetify/components')['VTabsWindow']
    VTabsWindowItem: typeof import('vuetify/components')['VTabsWindowItem']
    VTextField: typeof import('vuetify/components')['VTextField']
    VVirtualScroll: typeof import('vuetify/components')['VVirtualScroll']
  }
}
