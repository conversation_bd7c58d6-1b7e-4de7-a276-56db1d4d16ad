import { defineConfig } from "@rsbuild/core";
import { pluginVue } from "@rsbuild/plugin-vue";
import components from "unplugin-vue-components/rspack";
import autoImport from "unplugin-auto-import/rspack";
import { Vuetify3Resolver } from "unplugin-vue-components/resolvers";

export default defineConfig({
  html: {
    title: "链接云（个税计算器）",
    favicon: "./src/assets/logo.png",
    meta: {
      viewport:
        "width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no",
      description: "个人所得税计算器 - 支持多项目分别计算和合并计算",
    },
  },
  plugins: [pluginVue()],
  tools: {
    bundleAnalyze: {},
    rspack: {
      plugins: [
        components({
          resolvers: [Vuetify3Resolver()],
        }),
        autoImport({
          imports: ["vue", "vue-router"],
        }),
      ],
    },
  },
});
