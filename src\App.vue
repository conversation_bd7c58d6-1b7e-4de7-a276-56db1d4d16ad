<script setup lang="ts">
import { onMounted } from "vue";
import { useRouter } from "vue-router";
import { useTheme } from "vuetify";
import logo from "./assets/logo.png";
import {
  mdiWeatherSunny,
  mdiWeatherNight,
} from "@mdi/js";

const router = useRouter();
const theme = useTheme();

const goToHome = () => {
  router.push("/");
};

const toggleTheme = () => {
  theme.global.name.value = theme.global.current.value.dark ? "light" : "dark";
};

onMounted(() => {
  // 检测系统主题偏好
  const isDark = window.matchMedia("(prefers-color-scheme: dark)").matches;
  theme.global.name.value = isDark ? "dark" : "light";
});
</script>

<template>
  <v-app>
    <!-- 应用栏 -->
    <v-app-bar :elevation="2" color="primary" dark app>
      <template #prepend>
        <v-avatar
          :image="logo"
          size="40"
          class="me-3 cursor-pointer"
          @click="goToHome"
        />
      </template>

      <v-app-bar-title class="font-weight-bold"> 个税计算器 </v-app-bar-title>

      <template #append>
        <v-btn icon @click="toggleTheme">
          <v-icon
            :icon="
              theme.global.current.value.dark
                ? mdiWeatherSunny
                : mdiWeatherNight
            "
          />
        </v-btn>
      </template>
    </v-app-bar>

    <!-- 主要内容 -->
    <v-main>
      <v-container fluid class="pa-4">
        <router-view />
      </v-container>
    </v-main>

    <!-- 页脚 -->
    <v-footer class="mt-8">
      <v-container>
        <v-row justify="center">
          <v-col cols="12" lg="10" xl="8">
            <!-- 版权信息 -->
            <div class="text-center py-4">
              <v-chip variant="tonal" color="primary" size="small" class="mb-2">
                <span class="text-caption">免责声明</span>
              </v-chip>
              <p class="text-body-2 text-medium-emphasis mb-2 px-4">
                本计算器仅供参考，实际税额以税务部门认定为准
              </p>
              <p class="text-caption text-medium-emphasis px-4">
                &copy; {{ new Date().getFullYear() }} 个税计算器 ·
                基于国家税务总局最新政策制作
              </p>
            </div>
          </v-col>
        </v-row>
      </v-container>
    </v-footer>
  </v-app>
</template>
