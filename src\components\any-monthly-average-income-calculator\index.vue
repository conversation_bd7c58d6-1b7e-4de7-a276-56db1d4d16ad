<script setup lang="ts">
import { ref, computed } from "vue";
import {
  calculateTaxWithMergedResults,
  type ProjectTaxCalculationGroup,
  type ProjectIncomeData,
  type TaxCalculationGroup,
} from "../../utils/index";
import TaxResultTable from "../tax-result-table/index.vue";
import {
  mdiFolderMultiple,
  mdiDelete,
  mdiPlus,
  mdiCalculator,
  mdiRefresh,
  mdiChartLine,
  mdiMerge,
} from "@mdi/js";

interface MonthData {
  [key: string]: string;
  january: string;
  february: string;
  march: string;
  april: string;
  may: string;
  june: string;
  july: string;
  august: string;
  september: string;
  october: string;
  november: string;
  december: string;
}

interface ProjectData {
  id: string;
  name: string;
  months: MonthData;
}

const projects = ref<ProjectData[]>([
  {
    id: "project-1",
    name: "项目1",
    months: {
      january: "",
      february: "",
      march: "",
      april: "",
      may: "",
      june: "",
      july: "",
      august: "",
      september: "",
      october: "",
      november: "",
      december: "",
    },
  },
  {
    id: "project-2",
    name: "项目2",
    months: {
      january: "",
      february: "",
      march: "",
      april: "",
      may: "",
      june: "",
      july: "",
      august: "",
      september: "",
      october: "",
      november: "",
      december: "",
    },
  },
]);

const tableShow = ref(false);
const projectResults = ref<ProjectTaxCalculationGroup[]>([]);
const mergedResults = ref<TaxCalculationGroup[]>([]);
const loading = ref(false);
const expandedPanels = ref<string[]>(["project-1"]);
const resultTab = ref("projects");

const monthNames = [
  { key: "january", label: "1月", month: "01" },
  { key: "february", label: "2月", month: "02" },
  { key: "march", label: "3月", month: "03" },
  { key: "april", label: "4月", month: "04" },
  { key: "may", label: "5月", month: "05" },
  { key: "june", label: "6月", month: "06" },
  { key: "july", label: "7月", month: "07" },
  { key: "august", label: "8月", month: "08" },
  { key: "september", label: "9月", month: "09" },
  { key: "october", label: "10月", month: "10" },
  { key: "november", label: "11月", month: "11" },
  { key: "december", label: "12月", month: "12" },
];

// 计算项目汇总信息
const projectSummaries = computed(() => {
  return projects.value.map((project) => {
    const totalIncome = monthNames.reduce((sum, month) => {
      const income = Number(project.months[month.key as keyof MonthData]) || 0;
      return sum + income;
    }, 0);

    const monthCount = monthNames.filter((month) => {
      return Number(project.months[month.key as keyof MonthData]) > 0;
    }).length;

    return {
      projectId: project.id,
      totalIncome,
      monthCount,
    };
  });
});

// 判断是否需要虚拟化
const shouldUseVirtualScroll = computed(() => projects.value.length > 100);

const addProject = () => {
  const newProject: ProjectData = {
    id: `project-${Date.now()}`,
    name: `项目${projects.value.length + 1}`,
    months: {
      january: "",
      february: "",
      march: "",
      april: "",
      may: "",
      june: "",
      july: "",
      august: "",
      september: "",
      october: "",
      november: "",
      december: "",
    },
  };
  projects.value.push(newProject);
  expandedPanels.value.push(newProject.id);
};

const removeProject = (projectId: string) => {
  if (projects.value.length <= 1) {
    return;
  }
  projects.value = projects.value.filter((p) => p.id !== projectId);
  expandedPanels.value = expandedPanels.value.filter((id) => id !== projectId);
};

const handleSubmit = async () => {
  // 准备项目数据
  const projectsData: ProjectIncomeData[] = [];
  let hasData = false;

  projects.value.forEach((project) => {
    const monthIncomes: number[] = [];
    const monthNumbers: string[] = [];

    monthNames.forEach((month) => {
      const income = Number(project.months[month.key as keyof MonthData]);
      if (income > 0) {
        monthIncomes.push(income);
        monthNumbers.push(`第${month.month}月`);
        hasData = true;
      }
    });

    if (monthIncomes.length > 0) {
      projectsData.push({
        projectId: project.id,
        projectName: project.name,
        monthIncomes,
        monthNumbers,
      });
    }
  });

  if (!hasData) {
    return;
  }

  // 显示加载状态
  loading.value = true;
  tableShow.value = true;

  const results = calculateTaxWithMergedResults(projectsData);
  projectResults.value = results.projectResults;
  mergedResults.value = results.mergedResults;
  loading.value = false;
};

const handleReset = () => {
  // 重置到初始状态，包含默认的项目1和项目2
  projects.value = [
    {
      id: "project-1",
      name: "项目1",
      months: {
        january: "",
        february: "",
        march: "",
        april: "",
        may: "",
        june: "",
        july: "",
        august: "",
        september: "",
        october: "",
        november: "",
        december: "",
      },
    },
    {
      id: "project-2",
      name: "项目2",
      months: {
        january: "",
        february: "",
        march: "",
        april: "",
        may: "",
        june: "",
        july: "",
        august: "",
        september: "",
        october: "",
        november: "",
        december: "",
      },
    },
  ];

  // 重置其他状态
  expandedPanels.value = ["project-1"];
  tableShow.value = false;
  projectResults.value = [];
  mergedResults.value = [];
  loading.value = false;
  resultTab.value = "projects";
};
</script>

<template>
  <v-row class="fill-height">
    <!-- 左侧输入区域 -->
    <v-col cols="12" lg="5" xl="4">
      <v-card class="h-100 d-flex flex-column">
        <v-card-title class="bg-primary text-white d-flex align-center">
          <v-icon :icon="mdiFolderMultiple" class="me-2" />
          项目收入输入
          <v-spacer />
          <v-chip size="small" variant="outlined" color="white">
            {{ projects.length }} 个项目
          </v-chip>
        </v-card-title>

        <v-card-text class="pa-0 flex-grow-1 d-flex flex-column">
          <!-- 可滚动的项目区域 -->
          <div class="flex-grow-1 overflow-auto">
            <!-- 项目折叠面板 - 使用虚拟滚动 -->
            <v-virtual-scroll
              v-if="shouldUseVirtualScroll"
              :items="projects"
              :item-height="80"
              height="400"
            >
              <template v-slot:default="{ item: project, index }">
                <v-expansion-panels
                  v-model="expandedPanels"
                  multiple           
                  variant="accordion"
                >
                  <v-expansion-panel
                    :key="project.id"
                    :value="project.id"
                  >
                    <v-expansion-panel-title>
                      <v-row no-gutters align="center">
                        <v-col cols="8">
                          <v-text-field
                            v-model="project.name"
                            variant="plain"
                            density="compact"
                            hide-details
                            class="project-name-field"
                            @click.stop
                          />
                        </v-col>
                        <v-col cols="4" class="text-right">
                          <v-chip
                            size="small"
                            :color="
                              projectSummaries[index]?.totalIncome > 0
                                ? 'success'
                                : 'default'
                            "
                            variant="flat"
                          >
                            ¥{{
                              (
                                projectSummaries[index]?.totalIncome || 0
                              ).toLocaleString()
                            }}
                          </v-chip>
                        </v-col>
                      </v-row>
                    </v-expansion-panel-title>

                    <v-expansion-panel-text>
                      <v-row>
                        <v-col
                          v-for="month in monthNames"
                          :key="month.key"
                          cols="6"
                          sm="4"
                          md="3"
                        >
                          <v-text-field
                            v-model="project.months[month.key]"
                            :label="month.label"
                            type="number"
                          
                            variant="outlined"
                            density="compact"
                            hide-details
                            prefix="¥"
                          />
                        </v-col>
                      </v-row>

                      <v-divider class="my-3" />

                      <!-- 项目操作 -->
                      <div class="d-flex justify-space-between">
                        <v-chip size="small" color="info" variant="flat">
                          已填写 {{ projectSummaries[index]?.monthCount || 0 }} 个月
                        </v-chip>

                        <v-btn
                          color="error"
                          variant="outlined"
                          size="small"
                          :disabled="projects.length <= 1"
                          @click="removeProject(project.id)"
                          :prepend-icon="mdiDelete"
                        >
                          删除项目
                        </v-btn>
                      </div>
                    </v-expansion-panel-text>
                  </v-expansion-panel>
                </v-expansion-panels>
              </template>
            </v-virtual-scroll>

            <!-- 常规项目折叠面板 (少于100条时) -->
            <v-expansion-panels
              v-else
              v-model="expandedPanels"
              multiple
              variant="accordion"
            >
              <v-expansion-panel
                v-for="(project, index) in projects"
                :key="project.id"
                :value="project.id"
              >
              <v-expansion-panel-title>
                <v-row no-gutters align="center">
                  <v-col cols="8">
                    <v-text-field
                      v-model="project.name"
                      variant="plain"
                      density="compact"
                      hide-details
                      class="project-name-field"
                      @click.stop
                    />
                  </v-col>
                  <v-col cols="4" class="text-right">
                    <v-chip
                      size="small"
                      :color="
                        projectSummaries[index]?.totalIncome > 0
                          ? 'success'
                          : 'default'
                      "
                      variant="flat"
                    >
                      ¥{{
                        (
                          projectSummaries[index]?.totalIncome || 0
                        ).toLocaleString()
                      }}
                    </v-chip>
                  </v-col>
                </v-row>
              </v-expansion-panel-title>

              <v-expansion-panel-text>
                <v-row>
                  <v-col
                    v-for="month in monthNames"
                    :key="month.key"
                    cols="6"
                    sm="4"
                    md="3"
                  >
                    <v-text-field
                      v-model="project.months[month.key]"
                      :label="month.label"
                      type="number"
                      variant="outlined"
                      density="compact"
                      hide-details
                      prefix="¥"
                    />
                  </v-col>
                </v-row>

                <v-divider class="my-3" />

                <!-- 项目操作 -->
                <div class="d-flex justify-space-between">
                  <v-chip size="small" color="info" variant="flat">
                    已填写 {{ projectSummaries[index]?.monthCount || 0 }} 个月
                  </v-chip>

                  <v-btn
                    color="error"
                    variant="outlined"
                    size="small"
                    :disabled="projects.length <= 1"
                    @click="removeProject(project.id)"
                    :prepend-icon="mdiDelete"
                  >
                    删除项目
                  </v-btn>
                </div>
              </v-expansion-panel-text>
            </v-expansion-panel>
            </v-expansion-panels>
          </div>

          <!-- 添加项目和操作按钮 -->
          <div class="pa-4 flex-shrink-0">
            <v-btn
              block
              color="primary"
              variant="outlined"
              class="mb-3"
              @click="addProject"
              :prepend-icon="mdiPlus"
            >
              添加项目
            </v-btn>

            <v-row>
              <v-col cols="6">
                <v-btn
                  block
                  color="primary"
                  size="large"
                  :loading="loading"
                  @click="handleSubmit"
                  :prepend-icon="mdiCalculator"
                >
                  开始计算
                </v-btn>
              </v-col>
              <v-col cols="6">
                <v-btn
                  block
                  color="error"
                  variant="outlined"
                  size="large"
                  @click="handleReset"
                  :prepend-icon="mdiRefresh"
                >
                  重置
                </v-btn>
              </v-col>
            </v-row>
          </div>
        </v-card-text>
      </v-card>
    </v-col>

    <!-- 右侧结果区域 -->
    <v-col cols="12" lg="7" xl="8">
      <v-card v-if="tableShow" class="h-100 d-flex flex-column">
        <!-- 标题 -->
        <v-card-title class="bg-primary text-white flex-shrink-0">
          <div class="d-flex align-center">
            <v-icon :icon="mdiChartLine" class="me-2" />
            <span>税收计算结果</span>
          </div>
        </v-card-title>

        <v-tabs v-model="resultTab" color="primary" grow class="flex-shrink-0">
          <v-tab value="projects">
            <v-icon :icon="mdiFolderMultiple" class="me-2" />
            分项目计算
          </v-tab>
          <v-tab value="merged">
            <v-icon :icon="mdiMerge" class="me-2" />
            合并计算
          </v-tab>
        </v-tabs>

        <!-- 标签页内容 -->
        <v-tabs-window v-model="resultTab" class="flex-grow-1">
          <v-tabs-window-item value="projects" class="h-100">
            <div class="result-container">
              <!-- 虚拟滚动包装的结果表格 -->
              <div v-if="projectResults.length > 100" class="h-100">
                <v-virtual-scroll
                  :items="projectResults"
                  :item-height="80"
                  height="100%"
                >
                  <template v-slot:default="{ item }">
                    <TaxResultTable
                      :project-groups="[item]"
                      :loading="loading"
                      :show-summary="false"
                      class="mb-2"
                    />
                  </template>
                </v-virtual-scroll>
              </div>
              <!-- 常规表格 (少于100条时) -->
              <TaxResultTable
                v-else
                :project-groups="projectResults"
                :loading="loading"
                :show-summary="false"
              />
            </div>
          </v-tabs-window-item>

          <v-tabs-window-item value="merged" class="h-100">
            <div class="result-container">
              <div class="pa-4">
                <v-alert type="info" variant="tonal" class="mb-4">
                  <v-alert-title>合并计算说明</v-alert-title>
                  <p class="mb-0">
                    将所有项目的同月份收入合并后，按累计预扣法重新计算的结果
                  </p>
                </v-alert>

                <!-- 虚拟滚动包装的合并结果 -->
                <div v-if="mergedResults.length > 100" class="h-100">
                  <v-virtual-scroll
                    :items="mergedResults"
                    :item-height="100"
                    height="100%"
                  >
                    <template v-slot:default="{ item }">
                      <TaxResultTable
                        :groups="[item]"
                        :loading="loading"
                        :show-summary="false"
                        class="mb-2"
                      />
                    </template>
                  </v-virtual-scroll>
                </div>
                <!-- 常规表格 (少于100条时) -->
                <TaxResultTable
                  v-else
                  :groups="mergedResults"
                  :loading="loading"
                  :show-summary="true"
                />
              </div>
            </div>
          </v-tabs-window-item>
        </v-tabs-window>
      </v-card>

      <!-- 空状态 -->
      <v-card v-else class="h-100 d-flex align-center justify-center">
        <div class="text-center">
          <v-icon
            :icon="mdiCalculator"
            size="64"
            color="grey-lighten-2"
            class="mb-4"
          />
          <h3 class="text-h6 text-grey-darken-1 mb-2">等待计算</h3>
          <p class="text-body-2 text-grey">
            请在左侧输入项目收入信息，然后点击"开始计算"查看结果
          </p>
        </div>
      </v-card>
    </v-col>
  </v-row>
</template>

<style scoped>
.project-name-field :deep(.v-field__input) {
  font-weight: 600;
}

.h-100 {
  height: 100%;
}

.result-container {
  height: calc(100vh - 250px);
  min-height: 400px;
  overflow: auto;
}

.fill-height {
  height: calc(100vh - 180px);
  min-height: 600px;
}

.overflow-auto {
  overflow-y: auto;
  max-height: calc(100vh - 400px);
}

@media (max-width: 1264px) {
  .v-col {
    margin-bottom: 1rem;
  }
  
  .fill-height {
    height: auto;
    min-height: auto;
  }
  
  .result-container {
    height: 400px;
    min-height: 300px;
  }
  
  .overflow-auto {
    max-height: 300px;
  }
}
</style>
