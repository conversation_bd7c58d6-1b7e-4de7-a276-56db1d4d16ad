<script setup lang="ts">
import { ref, computed } from "vue";
import { saveAs } from "file-saver";
import * as XLSX from "xlsx";
import {
  calculateTaxWithMergedResults,
  type ProjectTaxCalculationGroup,
  type ProjectIncomeData,
} from "../../utils/index";
import TaxResultTable from "../tax-result-table/index.vue";
import {
  mdiUpload,
  mdiDownload,
  mdiFileExcel,
  mdiCalculator,
  mdiRefresh,
  mdiChartLine,
  mdiDelete,
  mdiFileSearch,
} from "@mdi/js";

interface MonthData {
  [key: string]: string;
  january: string;
  february: string;
  march: string;
  april: string;
  may: string;
  june: string;
  july: string;
  august: string;
  september: string;
  october: string;
  november: string;
  december: string;
}

interface ProjectData {
  id: string;
  name: string;
  months: MonthData;
}

const projects = ref<ProjectData[]>([]);
const tableShow = ref(false);
const projectResults = ref<ProjectTaxCalculationGroup[]>([]);
const loading = ref(false);
const fileInput = ref<HTMLInputElement>();
const dragover = ref(false);
const projectLabel = ref("姓名");
const hasUploadedFile = ref(false);
const isProcessing = ref(false);
const processingProgress = ref(0);
const processingStatus = ref("");
const totalRows = ref(0);

const monthNames = [
  { key: "january", label: "1月", month: "01" },
  { key: "february", label: "2月", month: "02" },
  { key: "march", label: "3月", month: "03" },
  { key: "april", label: "4月", month: "04" },
  { key: "may", label: "5月", month: "05" },
  { key: "june", label: "6月", month: "06" },
  { key: "july", label: "7月", month: "07" },
  { key: "august", label: "8月", month: "08" },
  { key: "september", label: "9月", month: "09" },
  { key: "october", label: "10月", month: "10" },
  { key: "november", label: "11月", month: "11" },
  { key: "december", label: "12月", month: "12" },
];

// 计算项目汇总信息
const projectSummaries = computed(() => {
  return projects.value.map((project) => {
    const totalIncome = monthNames.reduce((sum, month) => {
      const income = Number(project.months[month.key as keyof MonthData]) || 0;
      return sum + income;
    }, 0);

    const monthCount = monthNames.filter((month) => {
      return Number(project.months[month.key as keyof MonthData]) > 0;
    }).length;

    return {
      projectId: project.id,
      totalIncome,
      monthCount,
    };
  });
});

// 判断是否需要虚拟化
const shouldUseVirtualScroll = computed(() => projects.value.length > 100);

// 计算税收总和
const taxSummary = computed(() => {
  if (!tableShow.value) return null;
  
  const totalTax = projectResults.value.reduce((sum, result) => sum + result.projectTotalTax, 0);
  const totalIncome = projectResults.value.reduce((sum, result) => sum + result.projectTotalIncome, 0);
  const effectiveRate = totalIncome > 0 ? (totalTax / totalIncome) * 100 : 0;
  
  return {
    totalTax,
    totalIncome,
    effectiveRate,
    count: projectResults.value.length
  };
});

// Excel相关函数
const downloadTemplate = () => {
  const templateData = [
    {
      [projectLabel.value]: "张三",
      "1月": 10000,
      "2月": 12000,
      "3月": 0,
      "4月": 15000,
      "5月": 11000,
      "6月": 0,
      "7月": 13000,
      "8月": 14000,
      "9月": 0,
      "10月": 16000,
      "11月": 12000,
      "12月": 18000,
    },
    {
      [projectLabel.value]: "李四",
      "1月": 8000,
      "2月": 9000,
      "3月": 8500,
      "4月": 0,
      "5月": 0,
      "6月": 10000,
      "7月": 11000,
      "8月": 0,
      "9月": 12000,
      "10月": 13000,
      "11月": 0,
      "12月": 14000,
    },
  ];

  const worksheet = XLSX.utils.json_to_sheet(templateData);
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, "个税计算模板");

  const colWidths = [
    { wch: 10 }, // 项目/姓名
    { wch: 10 }, { wch: 10 }, { wch: 10 }, { wch: 10 },
    { wch: 10 }, { wch: 10 }, { wch: 10 }, { wch: 10 },
    { wch: 10 }, { wch: 10 }, { wch: 10 }, { wch: 10 },
  ];
  worksheet["!cols"] = colWidths;

  const excelBuffer = XLSX.write(workbook, { bookType: "xlsx", type: "array" });
  const blob = new Blob([excelBuffer], {
    type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  });
  saveAs(blob, `${projectLabel.value}个税计算模板.xlsx`);
};

// 导出计算结果 - 终极优化版本
const exportResults = () => {
  if (!tableShow.value || projectResults.value.length === 0) {
    alert("⚠️ 暂无可导出的数据\n\n请先进行税收计算后再导出");
    return;
  }

  try {
    // 开始导出状态（异步更新UI）
    setTimeout(() => {
      isProcessing.value = true;
      processingStatus.value = "正在导出...";
    }, 0);
    
    // 使用requestAnimationFrame确保UI更新后再处理
    requestAnimationFrame(() => {
      try {
        // 预分配数组大小，避免动态扩容
        const exportData = new Array(projectResults.value.length);
        
        // 创建快速查找表
        const projectMap = new Map(projects.value.map(p => [p.name, p]));
        
        // 批量处理数据，避免阻塞UI
        const processChunk = (startIndex: number) => {
          const endIndex = Math.min(startIndex + 1000, projectResults.value.length);
          
          for (let i = startIndex; i < endIndex; i++) {
            const result = projectResults.value[i];
            const project = projectMap.get(result.projectName);
            
            if (project) {
              const months = project.months;
              const totalIncome = result.projectTotalIncome;
              const totalTax = result.projectTotalTax; // 获取总税额

              exportData[i] = {
                [projectLabel.value]: project.name,
                "1月": months.january === '' ? '' : +months.january,
                "2月": months.february === '' ? '' : +months.february,
                "3月": months.march === '' ? '' : +months.march,
                "4月": months.april === '' ? '' : +months.april,
                "5月": months.may === '' ? '' : +months.may,
                "6月": months.june === '' ? '' : +months.june,
                "7月": months.july === '' ? '' : +months.july,
                "8月": months.august === '' ? '' : +months.august,
                "9月": months.september === '' ? '' : +months.september,
                "10月": months.october === '' ? '' : +months.october,
                "11月": months.november === '' ? '' : +months.november,
                "12月": months.december === '' ? '' : +months.december,
                "年度总收入": totalIncome,
                "年度累计预缴税金": totalTax
              };
            } else {
              exportData[i] = {
                [projectLabel.value]: result.projectName,
                "年度总收入": result.projectTotalIncome,
                "年度累计预缴税金": result.projectTotalTax
              };
            }
          }
          
          // 如果还有数据要处理，继续下一批
          if (endIndex < projectResults.value.length) {
            setTimeout(() => processChunk(endIndex), 0);
            return;
          }
          
          // 所有数据处理完成，生成Excel
          generateExcel(exportData);
        };
        
        // 开始处理第一批数据
        processChunk(0);
        
      } catch (error) {
        isProcessing.value = false;
        console.error("数据处理失败:", error);
        alert("⚠️ 数据处理失败，请稍后重试");
      }
    });
    
  } catch (error) {
    isProcessing.value = false;
    console.error("导出初始化失败:", error);
    alert("⚠️ 导出失败，请稍后重试");
  }
};

// 生成Excel文件的函数
const generateExcel = (exportData: any[]) => {
  try {
    // 使用最快的方式创建工作表
    const worksheet = XLSX.utils.json_to_sheet(exportData, {
      skipHeader: false,
      dateNF: 'yyyy-mm-dd' // 优化日期格式
    });
    
    // 快速设置列宽（避免map操作）
    worksheet["!cols"] = [
      { wch: 12 }, // 姓名
      { wch: 10 }, { wch: 10 }, { wch: 10 }, { wch: 10 }, // 1-4月
      { wch: 10 }, { wch: 10 }, { wch: 10 }, { wch: 10 }, // 5-8月
      { wch: 10 }, { wch: 10 }, { wch: 10 }, { wch: 10 }, // 9-12月
      { wch: 16 }, // 年度总收入
      { wch: 16 }  // 年度累计预缴税金
    ];

    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "个税计算结果");

    // 使用最快的写入选项
    const excelBuffer = XLSX.write(workbook, { 
      bookType: "xlsx", 
      type: "array",
      cellStyles: false, // 禁用样式加快速度
      sheetStubs: false  // 禁用空单元格处理
    });
    
    const blob = new Blob([excelBuffer], {
      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    });
    
    const fileName = `${projectLabel.value}个税计算结果_${new Date().toISOString().slice(0, 10)}.xlsx`;
    
    // 异步保存文件
    setTimeout(() => {
      saveAs(blob, fileName);
      isProcessing.value = false;
      
      // 清理内存
      exportData.length = 0;
      
      alert(`✅ 导出成功！\n\n已导出 ${projectResults.value.length} 个${projectLabel.value}的计算结果`);
    }, 0);
    
  } catch (error) {
    isProcessing.value = false;
    console.error("Excel生成失败:", error);
    alert("⚠️ Excel生成失败，请稍后重试");
  }
};



const handleFileUpload = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const file = target.files?.[0];
  if (file) {
    if (validateFile(file)) {
      parseExcelFile(file);
    }
  }
};

const handleDrop = (event: DragEvent) => {
  event.preventDefault();
  dragover.value = false;
  const files = event.dataTransfer?.files;
  if (files && files.length > 0) {
    if (validateFile(files[0])) {
      parseExcelFile(files[0]);
    }
  }
};

const handleDragOver = (event: DragEvent) => {
  event.preventDefault();
  dragover.value = true;
};

const handleDragLeave = () => {
  dragover.value = false;
};

// 文件验证函数
const validateFile = (file: File): boolean => {
  // 1. 检查文件类型
  const validTypes = [
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
    'application/vnd.ms-excel', // .xls
    'application/excel',
    'application/x-excel',
    'application/x-msexcel'
  ];
  
  const validExtensions = ['.xlsx', '.xls'];
  const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));
  
  if (!validTypes.includes(file.type) && !validExtensions.includes(fileExtension)) {
    alert("⚠️ 文件格式错误\n\n请上传 Excel 文件（.xlsx 或 .xls 格式）");
    return false;
  }

  // 2. 检查文件大小（限制为100MB，支持大文件）
  const maxSize = 100 * 1024 * 1024; // 100MB
  if (file.size > maxSize) {
    alert("⚠️ 文件过大\n\n文件大小不能超过 100MB，请压缩文件后重试");
    return false;
  }

  // 3. 检查文件名
  if (file.name.length > 100) {
    alert("⚠️ 文件名过长\n\n请使用较短的文件名（少于100个字符）");
    return false;
  }

  return true;
};

// 分批处理函数
const processChunks = async (data: any[], chunkSize: number = 1000): Promise<ProjectData[]> => {
  const results: ProjectData[] = [];
  const chunks = Math.ceil(data.length / chunkSize);
  
  for (let i = 0; i < chunks; i++) {
    const start = i * chunkSize;
    const end = Math.min(start + chunkSize, data.length);
    const chunk = data.slice(start, end);
    
    // 更新进度
    processingProgress.value = Math.round((i / chunks) * 100);
    processingStatus.value = `正在处理第 ${i + 1}/${chunks} 批数据...`;
    
    // 处理当前批次
    const chunkResults = await processDataChunk(chunk);
    results.push(...chunkResults);
    
    // 让出执行权，避免界面卡顿
    await new Promise(resolve => setTimeout(resolve, 10));
  }
  
  return results;
};

// 处理单个数据块
const processDataChunk = async (chunk: any[]): Promise<ProjectData[]> => {
  return chunk
    .filter((row: any) => {
      const firstColumn = Object.keys(row)[0];
      const name = row[firstColumn];
      return name && String(name).trim() !== "" && String(name).trim() !== "undefined";
    })
    .map((row: any, index) => {
      const firstColumn = Object.keys(row)[0];
      const months: MonthData = {
        january: "",
        february: "",
        march: "",
        april: "",
        may: "",
        june: "",
        july: "",
        august: "",
        september: "",
        october: "",
        november: "",
        december: "",
      };

      // 映射Excel的月份列到标准月份
      const monthMapping = {
        "1月": "january",
        "2月": "february", 
        "3月": "march",
        "4月": "april",
        "5月": "may",
        "6月": "june",
        "7月": "july",
        "8月": "august",
        "9月": "september",
        "10月": "october",
        "11月": "november",
        "12月": "december",
      };

      Object.entries(monthMapping).forEach(([excelMonth, monthKey]) => {
        const value = row[excelMonth];
        // 修正逻辑：正确处理 0 和空值
        if (value !== null && value !== undefined && String(value).trim() !== '') {
          const income = Number(value);
          if (!isNaN(income)) {
            // 是有效数字（包括0），则保留原始字符串
            months[monthKey as keyof MonthData] = String(value).trim();
          } else {
            // 不是数字，视为空白
            months[monthKey as keyof MonthData] = "";
          }
        } else {
          // 是空值，则为空白
          months[monthKey as keyof MonthData] = "";
        }
      });

      return {
        id: `project-${Date.now()}-${index}`,
        name: String(row[firstColumn]).trim(),
        months,
      };
    });
};

const parseExcelFile = async (file: File) => {
  // 开始处理状态
  isProcessing.value = true;
  processingProgress.value = 0;
  processingStatus.value = "正在读取Excel文件...";
  
  const reader = new FileReader();
  
  reader.onerror = () => {
    isProcessing.value = false;
    alert("⚠️ 文件读取失败\n\n请检查文件是否损坏或被占用");
  };
  
  reader.onload = async (e) => {
    try {
      processingStatus.value = "正在解析Excel数据...";
      processingProgress.value = 10;
      
      const data = new Uint8Array(e.target?.result as ArrayBuffer);
      const workbook = XLSX.read(data, { type: "array" });
      
      // 检查工作簿是否有效
      if (!workbook || !workbook.SheetNames || workbook.SheetNames.length === 0) {
        isProcessing.value = false;
        alert("⚠️ Excel文件无效\n\n文件中没有找到有效的工作表");
        return;
      }
      
      processingProgress.value = 20;
      
      const worksheet = workbook.Sheets[workbook.SheetNames[0]];
      
      // 检查工作表是否为空
      if (!worksheet) {
        isProcessing.value = false;
        alert("⚠️ 工作表为空\n\n请确保第一个工作表包含数据");
        return;
      }
      
      processingStatus.value = "正在转换数据格式...";
      processingProgress.value = 30;
      
      // 流式处理大文件
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { 
        defval: "", // 空值默认为空字符串
        raw: false  // 不使用原始值，避免日期等类型问题
      });

      // 检查数据是否为空
      if (!jsonData || jsonData.length === 0) {
        isProcessing.value = false;
        alert("⚠️ 数据为空\n\n请确保Excel文件包含有效数据：\n• 第一行为标题行\n• 至少包含一行数据");
        return;
      }

      // 大文件警告
      if (jsonData.length > 10000) {
        const confirmed = confirm(`⚠️ 检测到大文件\n\n文件包含 ${jsonData.length.toLocaleString()} 行数据，处理可能需要较长时间。\n\n是否继续处理？`);
        if (!confirmed) {
          isProcessing.value = false;
          return;
        }
      }

      totalRows.value = jsonData.length;
      processingProgress.value = 40;

      // 验证数据结构
      const firstRow = jsonData[0] as any;
      const columns = Object.keys(firstRow);
      
      if (columns.length < 2) {
        isProcessing.value = false;
        alert("⚠️ 数据格式错误\n\n请确保Excel文件至少包含2列：\n• 第一列：姓名/项目名\n• 其他列：月份收入数据");
        return;
      }
      
      const nameColumn = columns[0];
      
      // 检查月份列是否存在
      const monthColumns = ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"];
      const foundMonthColumns = monthColumns.filter(month => columns.includes(month));
      
      if (foundMonthColumns.length === 0) {
        isProcessing.value = false;
        alert("⚠️ 未找到月份数据\n\n请确保Excel文件包含月份列（如：1月、2月...12月）");
        return;
      }
      
      // 动态更新标签
      projectLabel.value = nameColumn;
      
      processingStatus.value = "正在处理数据...";
      processingProgress.value = 50;

      // 使用分批处理避免界面卡顿
      const parsedProjects = await processChunks(jsonData);

      processingProgress.value = 90;

      // 检查解析后的数据
      if (parsedProjects.length === 0) {
        isProcessing.value = false;
        alert("⚠️ 没有有效数据\n\n请检查：\n• 姓名/项目名列不能为空\n• 至少需要一行有效数据");
        return;
      }
      
      // 检查是否有收入数据
      const hasIncomeData = parsedProjects.some(project => 
        Object.values(project.months).some(value => Number(value) > 0)
      );
      
      if (!hasIncomeData) {
        isProcessing.value = false;
        alert("⚠️ 未找到收入数据\n\n请确保至少有一个月份包含大于0的收入金额");
        return;
      }

      processingStatus.value = "处理完成！";
      processingProgress.value = 100;

      projects.value = parsedProjects;
      tableShow.value = false;
      hasUploadedFile.value = true;
      
      // 延迟隐藏进度条
      setTimeout(() => {
        isProcessing.value = false;
      }, 500);
      
      // 成功提示
      alert(`✅ 文件处理成功！\n\n已导入 ${parsedProjects.length.toLocaleString()} 个${nameColumn}的数据\n找到 ${foundMonthColumns.length} 个月份的收入列\n\n处理时间：${((Date.now() - startTime) / 1000).toFixed(1)}秒`);
      
    } catch (error) {
      isProcessing.value = false;
      console.error("解析Excel文件失败:", error);
      
      // 更详细的错误信息
      let errorMessage = "⚠️ Excel文件解析失败\n\n";
      
      if (error instanceof Error) {
        if (error.message.includes("Unsupported file")) {
          errorMessage += "文件格式不受支持，请确保使用 .xlsx 或 .xls 格式";
        } else if (error.message.includes("Cannot read")) {
          errorMessage += "文件可能已损坏或被加密，请尝试重新保存文件";
        } else if (error.message.includes("Invalid")) {
          errorMessage += "文件内容无效，请检查文件是否为有效的Excel文件";
        } else if (error.message.includes("out of memory") || error.message.includes("Maximum call stack")) {
          errorMessage += "文件过大超出处理能力，请考虑分割文件或使用更强大的设备";
        } else {
          errorMessage += `解析错误：${error.message}`;
        }
      } else {
        errorMessage += "未知错误，请检查文件格式是否正确";
      }
      
      errorMessage += "\n\n建议：\n• 下载模板文件重新填写\n• 确保文件未被其他程序占用\n• 尝试另存为新的Excel文件\n• 考虑分割大文件为多个小文件";
      
      alert(errorMessage);
    }
  };
  
  const startTime = Date.now();
  reader.readAsArrayBuffer(file);
};

const triggerFileInput = () => {
  fileInput.value?.click();
};

const removeProject = (projectId: string) => {
  if (projects.value.length <= 1) {
    return;
  }
  projects.value = projects.value.filter((p) => p.id !== projectId);
};

const handleSubmit = async () => {
  // 准备项目数据
  const projectsData: ProjectIncomeData[] = [];
  let hasData = false;

  // 大数据量计算警告
  if (projects.value.length > 5000) {
    const confirmed = confirm(`⚠️ 大数据量计算\n\n即将计算 ${projects.value.length.toLocaleString()} 个${projectLabel.value}的税收，计算时间可能较长。\n\n是否继续？`);
    if (!confirmed) {
      return;
    }
  }

  // 开始计算状态
  isProcessing.value = true;
  processingProgress.value = 0;
  processingStatus.value = "正在准备计算数据...";

  // 分批准备数据，避免界面卡顿
  const chunks = Math.ceil(projects.value.length / 1000);
  
  for (let i = 0; i < chunks; i++) {
    const start = i * 1000;
    const end = Math.min(start + 1000, projects.value.length);
    const chunk = projects.value.slice(start, end);
    
    processingProgress.value = Math.round((i / chunks) * 30); // 前30%用于数据准备
    processingStatus.value = `正在准备第 ${i + 1}/${chunks} 批计算数据...`;
    
    chunk.forEach((project) => {
      const monthIncomes: number[] = [];
      const monthNumbers: string[] = [];

      monthNames.forEach((month) => {
        const income = Number(project.months[month.key as keyof MonthData]);
        if (income > 0) {
          monthIncomes.push(income);
          monthNumbers.push(`第${month.month}月`);
          hasData = true;
        }
      });

      if (monthIncomes.length > 0) {
        projectsData.push({
          projectId: project.id,
          projectName: project.name,
          monthIncomes,
          monthNumbers,
        });
      }
    });
    
    // 让出执行权
    await new Promise(resolve => setTimeout(resolve, 5));
  }

  if (!hasData) {
    isProcessing.value = false;
    alert("⚠️ 暂无数据\n\n请先上传包含收入数据的Excel文件");
    return;
  }

  processingStatus.value = "正在进行税收计算...";
  processingProgress.value = 40;

  // 显示加载状态
  loading.value = true;
  tableShow.value = true;

  try {
    // 分批计算，避免长时间阻塞
    const results = await calculateTaxWithProgress(projectsData);
    projectResults.value = results.projectResults;
    
    processingStatus.value = "计算完成！";
    processingProgress.value = 100;
    
    // 延迟隐藏进度条
    setTimeout(() => {
      isProcessing.value = false;
      loading.value = false;
    }, 500);
    
  } catch (error) {
    isProcessing.value = false;
    loading.value = false;
    console.error("计算失败:", error);
    alert("⚠️ 计算失败\n\n请检查数据格式或稍后重试");
  }
};

// 带进度的税收计算函数
const calculateTaxWithProgress = async (projectsData: ProjectIncomeData[]) => {
  const results = { projectResults: [] as ProjectTaxCalculationGroup[] };
  const chunks = Math.ceil(projectsData.length / 500); // 每批处理500个项目
  
  for (let i = 0; i < chunks; i++) {
    const start = i * 500;
    const end = Math.min(start + 500, projectsData.length);
    const chunk = projectsData.slice(start, end);
    
    // 更新进度 (40-95%)
    const progress = 40 + Math.round((i / chunks) * 55);
    processingProgress.value = progress;
    processingStatus.value = `正在计算第 ${i + 1}/${chunks} 批税收...`;
    
    // 计算当前批次
    const chunkResults = calculateTaxWithMergedResults(chunk);
    results.projectResults.push(...chunkResults.projectResults);
    
    // 让出执行权，避免界面卡顿
    await new Promise(resolve => setTimeout(resolve, 10));
  }
  
  return results;
};

const handleReset = () => {
  // 如果正在处理，询问用户确认
  if (isProcessing.value) {
    const confirmed = confirm("⚠️ 正在处理数据\n\n确定要中断当前操作并重置吗？");
    if (!confirmed) {
      return;
    }
  }
  
  projects.value = [];
  tableShow.value = false;
  projectResults.value = [];
  loading.value = false;
  projectLabel.value = "姓名";
  hasUploadedFile.value = false;
  isProcessing.value = false;
  processingProgress.value = 0;
  processingStatus.value = "";
  totalRows.value = 0;
  
  if (fileInput.value) {
    fileInput.value.value = "";
  }
};
</script>

<template>
  <v-row class="fill-height">
    <!-- 左侧上传和预览区域 -->
    <v-col cols="12" lg="5" xl="4">
      <v-card class="h-100 d-flex flex-column">
        <v-card-title class="bg-success text-white d-flex align-center">
          <v-icon :icon="mdiFileExcel" class="me-2" />
          Excel批量计算
          <v-spacer />
          <v-chip v-if="hasUploadedFile" size="small" variant="outlined" color="white">
            {{ projects.length }} 个{{ projectLabel }}
          </v-chip>
        </v-card-title>

        <v-card-text class="pa-0 flex-grow-1 d-flex flex-column">
          <!-- Excel操作区域 -->
          <div class="pa-4 border-b flex-shrink-0">
            <v-btn
              color="primary"
              variant="flat"
              block
              size="large"
              class="mb-3"
              @click="downloadTemplate"
              :prepend-icon="mdiDownload"
              :disabled="isProcessing"
            >
              下载Excel模板
            </v-btn>

            <input
              ref="fileInput"
              type="file"
              accept=".xlsx,.xls"
              style="display: none"
              @change="handleFileUpload"
            />

            <!-- 进度条显示 -->
            <v-card v-if="isProcessing" class="mb-3" variant="outlined">
              <v-card-text class="pa-4">
                <div class="d-flex align-center mb-2">
                  <v-icon icon="mdi-loading" class="me-2 rotating" color="primary" />
                  <span class="text-body-2 font-weight-medium">{{ processingStatus }}</span>
                </div>
                <v-progress-linear
                  :model-value="processingProgress"
                  height="8"
                  rounded
                  color="primary"
                  class="mb-2"
                />
                <div class="d-flex justify-space-between text-caption text-grey">
                  <span>{{ processingProgress }}%</span>
                  <span v-if="totalRows > 0">{{ totalRows.toLocaleString() }} 行数据</span>
                </div>
              </v-card-text>
            </v-card>

            <v-card
              v-else
              :class="[
                'upload-area',
                'pa-6',
                'text-center',
                'mb-3',
                dragover ? 'drag-over' : '',
              ]"
              variant="outlined"
              @click="triggerFileInput"
              @drop="handleDrop"
              @dragover="handleDragOver"
              @dragleave="handleDragLeave"
            >
              <v-icon :icon="mdiUpload" size="48" color="primary" class="mb-2" />
              <h3 class="text-h6 mb-2">上传Excel文件</h3>
              <p class="text-body-2 mb-0">
                点击选择或拖拽Excel文件到此区域
              </p>
              <p class="text-caption text-grey mt-1">
                支持 .xlsx 和 .xls 格式，最大100MB
              </p>
              <p class="text-caption text-success mt-1">
                ✨ 优化支持：可处理数万行数据
              </p>
            </v-card>
          </div>

          <!-- 数据预览区域 -->
          <div v-if="hasUploadedFile" class="flex-grow-1 overflow-auto">
            <div class="pa-4">
              <div class="d-flex align-center mb-3">
                <v-icon :icon="mdiFileSearch" class="me-2" />
                <h4 class="text-h6">数据预览</h4>
                <v-spacer />
                <v-chip size="small" color="info">
                  共 {{ projects.length }} 条记录
                </v-chip>
              </div>

              <!-- 数据预览表格 -->
              <v-card variant="outlined" class="mb-4">
                <v-data-table
                  :items="projects.slice(0, 10)"
                  :headers="[
                    { title: projectLabel, key: 'name' },
                    { title: '总收入', key: 'totalIncome' },
                    { title: '有效月份', key: 'monthCount' },
                    { title: '操作', key: 'actions', sortable: false }
                  ]"
                  :items-per-page="10"
                  hide-default-footer
                  density="compact"
                >
                  <template v-slot:item.totalIncome="{ item, index }">
                    ¥{{ (projectSummaries[index]?.totalIncome || 0).toLocaleString() }}
                  </template>
                  <template v-slot:item.monthCount="{ item, index }">
                    {{ projectSummaries[index]?.monthCount || 0 }} 个月
                  </template>
                  <template v-slot:item.actions="{ item }">
                    <v-btn
                      color="error"
                      variant="text"
                      size="small"
                      icon
                      :disabled="projects.length <= 1"
                      @click="removeProject(item.id)"
                    >
                      <v-icon :icon="mdiDelete" />
                    </v-btn>
                  </template>
                </v-data-table>
                
                <v-card-text v-if="projects.length > 10" class="text-center text-grey">
                  还有 {{ projects.length - 10 }} 条记录未显示...
                </v-card-text>
              </v-card>
            </div>
          </div>

          <!-- 操作按钮区域 -->
          <div class="pa-4 flex-shrink-0">
            <v-row>
              <v-col cols="6">
                <v-btn
                  block
                  color="success"
                  size="large"
                  :loading="loading || isProcessing"
                  :disabled="!hasUploadedFile || isProcessing"
                  @click="handleSubmit"
                  :prepend-icon="mdiCalculator"
                >
                  开始计算
                </v-btn>
              </v-col>
              <v-col cols="6">
                <v-btn
                  block
                  color="error"
                  variant="outlined"
                  size="large"
                  @click="handleReset"
                  :prepend-icon="mdiRefresh"
                >
                  重置
                </v-btn>
              </v-col>
            </v-row>
          </div>
        </v-card-text>
      </v-card>
    </v-col>

    <!-- 右侧结果区域 -->
    <v-col cols="12" lg="7" xl="8">
      <v-card v-if="tableShow" class="h-100 d-flex flex-column">
        <!-- 标题和税收汇总 -->
        <v-card-title class="bg-success text-white flex-shrink-0 pa-0">
          <!-- 主标题区域 -->
          <div class="d-flex w-100 align-center justify-space-between pa-3 border-bottom">
            <div class="d-flex align-center">
              <v-icon :icon="mdiChartLine" class="me-2" />
              <span>批量计算结果</span>
            </div>
            <v-btn
              color="white"
              variant="outlined"
              size="small"
              :disabled="isProcessing"
              :loading="isProcessing"
              @click="exportResults"
              :prepend-icon="mdiFileExcel"
            >
              导出Excel
            </v-btn>
          </div>
          
          <!-- 税收汇总信息栏 -->
          <div v-if="taxSummary" class="tax-summary-bar px-3 py-2">
            <div class="d-flex align-center justify-space-between">
              <div class="summary-item">
                <span class="summary-label">计算对象</span>
                <span class="summary-value">{{ taxSummary.count }}个{{ projectLabel }}</span>
              </div>
              <div class="summary-divider"></div>
              <div class="summary-item">
                <span class="summary-label">总收入</span>
                <span class="summary-value highlight-income">¥{{ taxSummary.totalIncome.toLocaleString() }}</span>
              </div>
              <div class="summary-divider"></div>
              <div class="summary-item">
                <span class="summary-label">总税额</span>
                <span class="summary-value highlight-tax">¥{{ taxSummary.totalTax.toLocaleString() }}</span>
              </div>
              <div class="summary-divider"></div>
              <div class="summary-item">
                <span class="summary-label">有效税率</span>
                <span class="summary-value highlight-rate">{{ taxSummary.effectiveRate.toFixed(2) }}%</span>
              </div>
            </div>
          </div>
        </v-card-title>

        <!-- 结果表格 -->
        <div class="flex-grow-1">
          <div class="result-container">
            <!-- 虚拟滚动包装的结果表格 -->
            <div v-if="projectResults.length > 100" class="h-100">
              <v-virtual-scroll
                :items="projectResults"
                :item-height="200"
                height="100%"
              >
                <template v-slot:default="{ item }">
                  <TaxResultTable
                    :project-groups="[item]"
                    :loading="loading"
                    :show-summary="false"
                  />
                </template>
              </v-virtual-scroll>
            </div>
            <!-- 常规表格 (少于100条时) -->
            <TaxResultTable
              v-else
              :project-groups="projectResults"
              :loading="loading"
              :show-summary="false"
            />
          </div>
        </div>
      </v-card>

      <!-- 空状态 -->
      <v-card v-else class="h-100 d-flex align-center justify-center">
        <div class="text-center">
          <v-icon
            :icon="mdiFileExcel"
            size="64"
            color="grey-lighten-2"
            class="mb-4"
          />
          <h3 class="text-h6 text-grey-darken-1 mb-2">等待批量计算</h3>
          <p class="text-body-2 text-grey mb-4">
            请先上传包含收入数据的Excel文件，然后点击"开始计算"查看结果
          </p>
          <v-btn
            v-if="!hasUploadedFile"
            color="success"
            variant="flat"
            @click="triggerFileInput"
            :prepend-icon="mdiUpload"
          >
            上传Excel文件
          </v-btn>
        </div>
      </v-card>
    </v-col>
  </v-row>
</template>

<style scoped>
.h-100 {
  height: 100%;
}

.upload-area {
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px dashed #ccc !important;
  min-height: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.upload-area:hover {
  border-color: rgb(var(--v-theme-success)) !important;
  background-color: rgba(var(--v-theme-success), 0.05);
}

.drag-over {
  border-color: rgb(var(--v-theme-success)) !important;
  background-color: rgba(var(--v-theme-success), 0.1);
}

.border-b {
  border-bottom: 1px solid rgba(var(--v-border-color), 0.12);
}

.result-container {
  height: calc(100vh - 250px);
  min-height: 400px;
  overflow: auto;
}

.fill-height {
  height: calc(100vh - 180px);
  min-height: 600px;
}

.overflow-auto {
  overflow-y: auto;
  max-height: calc(100vh - 400px);
}

/* 税收汇总信息栏样式 */
.tax-summary-bar {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(8px);
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  min-width: 120px;
}

.summary-label {
  font-size: 12px;
  opacity: 0.9;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.summary-value {
  font-size: 16px;
  font-weight: 700;
  letter-spacing: 0.5px;
}

.highlight-income {
  color: #81C784;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.highlight-tax {
  color: #FFB74D;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.highlight-rate {
  color: #64B5F6;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.summary-divider {
  width: 1px;
  height: 40px;
  background: rgba(255, 255, 255, 0.3);
  margin: 0 8px;
}

.border-bottom {
  border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
}

@media (max-width: 1264px) {
  .v-col {
    margin-bottom: 1rem;
  }
  
  .fill-height {
    height: auto;
    min-height: auto;
  }
  
  .result-container {
    height: 400px;
    min-height: 300px;
  }
  
  .overflow-auto {
    max-height: 300px;
  }
  
  /* 小屏幕上的税收汇总样式调整 */
  .tax-summary-bar .d-flex {
    flex-wrap: wrap;
    gap: 8px;
    justify-content: center;
  }
  
  .summary-item {
    min-width: 100px;
    flex: 1;
  }
  
  .summary-divider {
    display: none;
  }
  
  .summary-label {
    font-size: 11px;
  }
  
  .summary-value {
    font-size: 14px;
  }
}

/* 旋转动画 */
.rotating {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style> 
