<script setup lang="ts">
import { reactive, ref } from "vue";
import {
  calculateTaxByMonthsGrouped,
  type TaxCalculationGroup,
} from "../../utils/index";
import TaxResultTable from "../tax-result-table/index.vue";
import {
  mdiCalendarMonth,
  mdiCalculator,
  mdiRefresh,
  mdiChartLine,
} from "@mdi/js";

interface FormData {
  money: string;
  month: string;
}

const formData = reactive<FormData>({
  money: "",
  month: "",
});

const tableShow = ref(false);
const tableGroups = ref<TaxCalculationGroup[]>([]);
const loading = ref(false);
const valid = ref(false);

const rules = {
  money: [
    (v: string) => !!v || "月均收入不能为空",
    (v: string) => Number(v) >= 0 || "月均收入不能小于0",
  ],
  month: [
    (v: string) => !!v || "连续收入月数不能为空",
    (v: string) => {
      const num = Number(v);
      return (
        (num > 0 && num <= 12 && Number.isInteger(num)) ||
        "请输入1-12之间的整数"
      );
    },
  ],
};

const handleSubmit = async () => {
  if (!valid.value) return;

  const money = Number(formData.money);
  const monthCount = Number(formData.month);

  loading.value = true;
  tableShow.value = true;

  // 生成月收入数据
  const monthIncomes = Array(monthCount).fill(money);
  const monthNumbers = Array.from(
    { length: monthCount },
    (_, i) => `第${String(i + 1).padStart(2, "0")}月`
  );

  const results = calculateTaxByMonthsGrouped(monthIncomes, monthNumbers);
  tableGroups.value = results;
  loading.value = false;
};

const handleReset = () => {
  formData.money = "";
  formData.month = "";
  tableShow.value = false;
  tableGroups.value = [];
  loading.value = false;
};
</script>

<template>
  <v-row>
    <!-- 输入区域 -->
    <v-col cols="12" md="6" lg="4">
      <v-card>
        <v-card-title class="bg-primary text-white">
          <v-icon :icon="mdiCalendarMonth" class="me-2" />
          月均收入设置
        </v-card-title>

        <v-card-text>
          <v-alert type="info" variant="tonal" class="mb-4">
            适用于连续每个月收入金额相同的情况
          </v-alert>

          <v-form v-model="valid">
            <v-text-field
              v-model="formData.money"
              label="月均收入"
              type="number"
              variant="outlined"
              prefix="¥"
              :rules="rules.money"
              class="mb-4"
              placeholder="请输入每月固定收入金额"
            />

            <v-text-field
              v-model="formData.month"
              label="连续月数"
              type="number"
              variant="outlined"
              :rules="rules.month"
              class="mb-4"
              placeholder="请输入1-12之间的月数"
              hint="表示连续几个月都有这个收入"
              persistent-hint
            />

            <v-row>
              <v-col cols="6">
                <v-btn
                  block
                  color="primary"
                  size="large"
                  :disabled="!valid"
                  :loading="loading"
                  @click="handleSubmit"
                  :prepend-icon="mdiCalculator"
                >
                  开始计算
                </v-btn>
              </v-col>
              <v-col cols="6">
                <v-btn
                  block
                  color="error"
                  variant="outlined"
                  size="large"
                  @click="handleReset"
                  :prepend-icon="mdiRefresh"
                >
                  重置
                </v-btn>
              </v-col>
            </v-row>
          </v-form>
        </v-card-text>
      </v-card>
    </v-col>

    <!-- 结果区域 -->
    <v-col cols="12" md="6" lg="8">
      <v-card v-if="tableShow">
        <v-card-title class="bg-success text-white">
          <v-icon :icon="mdiChartLine" class="me-2" />
          计算结果
        </v-card-title>

        <TaxResultTable
          :groups="tableGroups"
          :loading="loading"
          :show-summary="true"
        />
      </v-card>

      <!-- 空状态 -->
      <v-card v-else class="h-100 d-flex align-center justify-center">
        <div class="text-center">
          <v-icon
            :icon="mdiCalculator"
            size="64"
            color="grey-lighten-2"
            class="mb-4"
          />
          <h3 class="text-h6 text-grey-darken-1 mb-2">等待计算</h3>
          <p class="text-body-2 text-grey">
            请在左侧输入月均收入和连续月数，然后点击"开始计算"查看结果
          </p>
        </div>
      </v-card>
    </v-col>
  </v-row>
</template>

<style scoped>
.h-100 {
  height: 100%;
}
</style>
