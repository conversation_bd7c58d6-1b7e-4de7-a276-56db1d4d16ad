<script setup lang="ts">
import { computed, ref } from "vue";
import { mdiFolder, mdiCalendarRange, mdiChartBox } from "@mdi/js";
import type {
  TaxCalculationResult,
  TaxCalculationGroup,
  ProjectTaxCalculationGroup,
} from "../../types/tax";

interface Props {
  data?: TaxCalculationResult[];
  groups?: TaxCalculationGroup[];
  projectGroups?: ProjectTaxCalculationGroup[];
  loading?: boolean;
  showSummary?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  showSummary: true,
});

// 表格列定义
const headers = [
  { title: "月份", key: "month", align: "center" as const, width: 100 },
  { title: "本期收入", key: "income", align: "end" as const, width: 120 },
  {
    title: "累计收入",
    key: "cumulativeIncome",
    align: "end" as const,
    width: 120,
  },
  {
    title: "累计费用",
    key: "cumulativeCost",
    align: "end" as const,
    width: 120,
  },
  {
    title: "累计减除费用",
    key: "cumulativeFixedDeduct",
    align: "end" as const,
    width: 120,
  },
  {
    title: "累计应纳税所得额",
    key: "cumulativeTaxableIncome",
    align: "end" as const,
    width: 140,
  },
  { title: "税率", key: "rate", align: "center" as const, width: 80 },
  {
    title: "速算扣除数",
    key: "quickDeduction",
    align: "end" as const,
    width: 120,
  },
  {
    title: "累计应缴个税",
    key: "cumulativeTax",
    align: "end" as const,
    width: 120,
  },
  {
    title: "上期累计个税",
    key: "lastCumulativeTax",
    align: "end" as const,
    width: 120,
  },
  {
    title: "本期应缴个税",
    key: "currentTax",
    align: "end" as const,
    width: 120,
  },
];

// 格式化数据用于表格显示
const formatTableData = (data: TaxCalculationResult[]) => {
  return data.map((item) => ({
    ...item,
    income: `¥${item.income.toLocaleString()}`,
    cumulativeIncome: `¥${item.cumulativeIncome.toLocaleString()}`,
    cumulativeCost: `¥${item.cumulativeCost.toLocaleString()}`,
    cumulativeFixedDeduct: `¥${item.cumulativeFixedDeduct.toLocaleString()}`,
    cumulativeTaxableIncome: `¥${item.cumulativeTaxableIncome.toLocaleString()}`,
    rate: `${(item.rate * 100).toFixed(1)}%`,
    quickDeduction: `¥${item.quickDeduction.toLocaleString()}`,
    cumulativeTax: `¥${item.cumulativeTax.toLocaleString()}`,
    lastCumulativeTax: `¥${item.lastCumulativeTax.toLocaleString()}`,
    currentTax: `¥${item.currentTax.toLocaleString()}`,
  }));
};

// 获取所有数据（用于计算整体摘要）
const allData = computed(() => {
  if (props.data) return props.data;
  if (props.groups) return props.groups.flatMap((group) => group.data);
  if (props.projectGroups) {
    return props.projectGroups.flatMap((project) =>
      project.groups.flatMap((group) => group.data)
    );
  }
  return [];
});

// 计算总计数据（基于所有数据）
const summaryData = computed(() => {
  if (!allData.value.length) return null;

  const totalIncome = allData.value.reduce(
    (sum, item) => sum + item.income,
    0
  );
  const totalTax = allData.value.reduce(
    (sum, item) => sum + item.currentTax,
    0
  );

  return {
    months: allData.value.length,
    totalIncome,
    totalTax,
    effectiveRate: totalIncome > 0 ? (totalTax / totalIncome) * 100 : 0,
  };
});

// 判断是否使用分组模式
const isGroupMode = computed(() => props.groups && props.groups.length > 0);

// 判断是否使用项目分组模式
const isProjectMode = computed(
  () => props.projectGroups && props.projectGroups.length > 0
);

const expandedProjects = ref<string[]>([]);
</script>

<template>
  <div class="tax-result-table-container">
    <!-- 加载状态 -->
    <div v-if="loading" class="d-flex justify-center align-center pa-8">
      <v-progress-circular indeterminate color="primary" size="48" />
      <span class="ml-4 text-h6">正在计算...</span>
    </div>

    <!-- 项目分组模式：显示多个项目的多个表格 -->
    <div v-else-if="isProjectMode && projectGroups">
      <v-expansion-panels
        v-model="expandedProjects"
        multiple
        variant="accordion"
      >
        <v-expansion-panel
          v-for="project in projectGroups"
          :key="project.projectId"
          :value="project.projectId"
        >
          <v-expansion-panel-title class="project-panel-title">
            <v-row no-gutters align="center">
              <v-col cols="6">
                <div class="d-flex align-center">
                  <v-icon :icon="mdiFolder" class="me-2" color="primary" />
                  <span class="text-h6 font-weight-bold">{{
                    project.projectName
                  }}</span>
                </div>
              </v-col>
              <v-col cols="6" class="text-right">
                <v-chip color="success" variant="flat" class="me-2">
                  收入：¥{{ project.projectTotalIncome.toLocaleString() }}
                </v-chip>
                <v-chip color="warning" variant="flat">
                  个税：¥{{ project.projectTotalTax.toLocaleString() }}
                </v-chip>
              </v-col>
            </v-row>
          </v-expansion-panel-title>

          <v-expansion-panel-text>
            <!-- 项目下的分组表格 -->
            <div
              v-for="group in project.groups"
              :key="group.groupIndex"
              class="mb-6"
            >
              <v-card variant="outlined">
                <v-card-title class="bg-grey-lighten-4 py-3">
                  <v-icon
                    :icon="mdiCalendarRange"
                    class="me-2"
                    color="primary"
                  />
                  {{ group.title }}
                </v-card-title>

                <v-data-table
                  :headers="headers"
                  :items="formatTableData(group.data)"
                  :items-per-page="-1"
                  hide-default-footer
                  class="project-data-table"
                  density="compact"
                >
                  <template #item.month="{ item }">
                    <v-chip size="small" color="primary" variant="flat">
                      {{ item.month }}
                    </v-chip>
                  </template>

                  <template #item.currentTax="{ item }">
                    <span class="font-weight-bold text-warning">
                      {{ item.currentTax }}
                    </span>
                  </template>

                  <template #item.rate="{ item }">
                    <v-chip size="small" color="success" variant="flat">
                      {{ item.rate }}
                    </v-chip>
                  </template>
                </v-data-table>
              </v-card>
            </div>
          </v-expansion-panel-text>
        </v-expansion-panel>
      </v-expansion-panels>
    </div>

    <!-- 分组模式：显示多个表格 -->
    <div v-else-if="isGroupMode && groups">
      <!-- 整体摘要 -->
      <v-card
        v-if="showSummary && summaryData"
        class="mb-4"
        color="primary"
        variant="tonal"
      >
        <v-card-title>
          <v-icon :icon="mdiChartBox" class="me-2" />
          计算摘要
        </v-card-title>
        <v-card-text>
          <v-row>
            <v-col cols="6" sm="3">
              <v-card variant="flat" color="info" class="pa-3 text-center">
                <div class="text-h6 font-weight-bold">
                  {{ summaryData.months }}
                </div>
                <div class="text-body-2">计算月数</div>
              </v-card>
            </v-col>
            <v-col cols="6" sm="3">
              <v-card variant="flat" color="success" class="pa-3 text-center">
                <div class="text-h6 font-weight-bold">
                  ¥{{ summaryData.totalIncome.toLocaleString() }}
                </div>
                <div class="text-body-2">总收入</div>
              </v-card>
            </v-col>
            <v-col cols="6" sm="3">
              <v-card variant="flat" color="warning" class="pa-3 text-center">
                <div class="text-h6 font-weight-bold">
                  ¥{{ summaryData.totalTax.toLocaleString() }}
                </div>
                <div class="text-body-2">总个税</div>
              </v-card>
            </v-col>
            <v-col cols="6" sm="3">
              <v-card variant="flat" color="primary" class="pa-3 text-center">
                <div class="text-h6 font-weight-bold">
                  {{ summaryData.effectiveRate.toFixed(2) }}%
                </div>
                <div class="text-body-2">实际税率</div>
              </v-card>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>

      <!-- 分组提示 -->
      <v-alert
        v-if="groups.length > 1"
        type="info"
        variant="tonal"
        class="mb-4"
      >
        <v-alert-title
          >检测到 {{ groups.length }} 个独立的计算周期</v-alert-title
        >
        <p class="mb-0">已按收入连续性分组在下方显示，每组独立计算累计个税</p>
      </v-alert>

      <!-- 分组表格 -->
      <div v-for="group in groups" :key="group.groupIndex" class="mb-6">
        <v-card>
          <v-card-title v-if="groups.length > 1" class="bg-primary text-white">
            <v-icon :icon="mdiCalendarRange" class="me-2" />
            {{ group.title }}
          </v-card-title>

          <v-data-table
            :headers="headers"
            :items="formatTableData(group.data)"
            :items-per-page="-1"
            hide-default-footer
            class="group-data-table"
            density="compact"
          >
            <template #item.month="{ item }">
              <v-chip size="small" color="primary" variant="flat">
                {{ item.month }}
              </v-chip>
            </template>

            <template #item.currentTax="{ item }">
              <span class="font-weight-bold text-warning">
                {{ item.currentTax }}
              </span>
            </template>

            <template #item.rate="{ item }">
              <v-chip size="small" color="success" variant="flat">
                {{ item.rate }}
              </v-chip>
            </template>
          </v-data-table>
        </v-card>
      </div>
    </div>

    <!-- 单表模式：显示单个表格 -->
    <div v-else-if="data && data.length > 0">
      <!-- 整体摘要 -->
      <v-card
        v-if="showSummary && summaryData"
        class="mb-4"
        color="primary"
        variant="tonal"
      >
        <v-card-title>
          <v-icon :icon="mdiChartBox" class="me-2" />
          计算摘要
        </v-card-title>
        <v-card-text>
          <v-row>
            <v-col cols="6" sm="3">
              <v-card variant="flat" color="info" class="pa-3 text-center">
                <div class="text-h6 font-weight-bold">
                  {{ summaryData.months }}
                </div>
                <div class="text-body-2">计算月数</div>
              </v-card>
            </v-col>
            <v-col cols="6" sm="3">
              <v-card variant="flat" color="success" class="pa-3 text-center">
                <div class="text-h6 font-weight-bold">
                  ¥{{ summaryData.totalIncome.toLocaleString() }}
                </div>
                <div class="text-body-2">总收入</div>
              </v-card>
            </v-col>
            <v-col cols="6" sm="3">
              <v-card variant="flat" color="warning" class="pa-3 text-center">
                <div class="text-h6 font-weight-bold">
                  ¥{{ summaryData.totalTax.toLocaleString() }}
                </div>
                <div class="text-body-2">总个税</div>
              </v-card>
            </v-col>
            <v-col cols="6" sm="3">
              <v-card variant="flat" color="primary" class="pa-3 text-center">
                <div class="text-h6 font-weight-bold">
                  {{ summaryData.effectiveRate.toFixed(2) }}%
                </div>
                <div class="text-body-2">实际税率</div>
              </v-card>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>

      <v-card>
        <v-data-table
          :headers="headers"
          :items="formatTableData(data)"
          :items-per-page="-1"
          hide-default-footer
          class="single-data-table"
          density="compact"
        >
          <template #item.month="{ item }">
            <v-chip size="small" color="primary" variant="flat">
              {{ item.month }}
            </v-chip>
          </template>

          <template #item.currentTax="{ item }">
            <span class="font-weight-bold text-warning">
              {{ item.currentTax }}
            </span>
          </template>

          <template #item.rate="{ item }">
            <v-chip size="small" color="success" variant="flat">
              {{ item.rate }}
            </v-chip>
          </template>
        </v-data-table>
      </v-card>
    </div>
  </div>
</template>

<style scoped>
.tax-result-table-container {
  width: 100%;
}

.project-panel-title {
  background: linear-gradient(135deg, #e3f2fd 0%, #ffffff 100%);
}

/* 暗黑模式下的项目面板标题背景 */
.v-theme--dark .project-panel-title {
  background: linear-gradient(135deg, #1a237e 0%, #303f9f 100%);
}

.project-data-table :deep(.v-data-table__wrapper) {
  overflow-x: auto;
}

.group-data-table :deep(.v-data-table__wrapper) {
  overflow-x: auto;
}

.single-data-table :deep(.v-data-table__wrapper) {
  overflow-x: auto;
}

/* 表格样式优化 */
:deep(.v-data-table-header__content) {
  font-weight: 600;
}

:deep(.v-data-table__td) {
  white-space: nowrap;
}

/* 响应式表格 */
@media (max-width: 768px) {
  :deep(.v-data-table) {
    font-size: 12px;
  }

  :deep(.v-data-table__td) {
    padding: 0 8px;
  }
}
</style>
