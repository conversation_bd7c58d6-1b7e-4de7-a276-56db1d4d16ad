export interface TaxRateLevel {
  min: number;
  max: number;
  rate: number;
  quickDeduction: number;
}

export interface TaxCalculationResult {
  month: string;
  income: number;
  cumulativeIncome: number;
  cumulativeCost: number;
  cumulativeFixedDeduct: number;
  cumulativeTaxableIncome: number;
  rate: number;
  quickDeduction: number;
  cumulativeTax: number;
  lastCumulativeTax: number;
  currentTax: number;
}

export interface TaxCalculationGroup {
  groupIndex: number;
  title: string;
  data: TaxCalculationResult[];
}

export interface ProjectTaxCalculationGroup {
  projectId: string;
  projectName: string;
  groups: TaxCalculationGroup[];
  projectTotalIncome: number;
  projectTotalTax: number;
}

export interface ProjectIncomeData {
  projectId: string;
  projectName: string;
  monthIncomes: number[];
  monthNumbers: string[];
}
