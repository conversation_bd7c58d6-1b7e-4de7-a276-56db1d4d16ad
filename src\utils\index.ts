import type {
  TaxRateLevel,
  TaxCalculationResult,
  TaxCalculationGroup,
  ProjectTaxCalculationGroup,
  ProjectIncomeData,
} from "../types/tax";

const COST_DEDUCTION_RATE = 0.2; // 成本扣除率
const MONTHLY_FIXED_DEDUCTION = 5000; // 每月固定扣除

// 税率表级次
const taxRateLevels: TaxRateLevel[] = [
  { min: 0, max: 36000, rate: 0.03, quickDeduction: 0 },
  { min: 36000, max: 144000, rate: 0.1, quickDeduction: 2520 },
  { min: 144000, max: 300000, rate: 0.2, quickDeduction: 16920 },
  { min: 300000, max: 420000, rate: 0.25, quickDeduction: 31920 },
  { min: 420000, max: 660000, rate: 0.3, quickDeduction: 52920 },
  { min: 660000, max: 960000, rate: 0.35, quickDeduction: 85920 },
  { min: 960000, max: Number.MAX_VALUE, rate: 0.45, quickDeduction: 181920 },
];

// 根据累计应纳税所得额查找税率级次
function getTaxRateLevel(taxableIncome: number): TaxRateLevel {
  for (const level of taxRateLevels) {
    if (taxableIncome > level.min && taxableIncome <= level.max) {
      return level;
    }
  }
  return taxRateLevels[0];
}

// 批量分月累计预扣法计算 - 原始函数，返回单一数组
export function calculateTaxByMonths(
  monthIncomes: number[],
  monthNumbers: string[]
): TaxCalculationResult[] {
  const groups = calculateTaxByMonthsGrouped(monthIncomes, monthNumbers);
  return groups.flatMap((group) => group.data);
}

// 批量分月累计预扣法计算 - 新函数，返回分组结果
export function calculateTaxByMonthsGrouped(
  monthIncomes: number[],
  monthNumbers: string[]
): TaxCalculationGroup[] {
  const groups: TaxCalculationGroup[] = [];
  let currentGroup: TaxCalculationResult[] = [];
  let groupIndex = 0;

  let cumulativeIncome = 0;
  let cumulativeCost = 0;
  let cumulativeFixedDeduct = 0;
  let cumulativeTaxableIncome = 0;
  let lastCumulativeTax = 0;
  let lastMonth: string | null = null;
  let continuousMonths = 0;
  let groupStartMonth = "";

  for (let i = 0; i < monthIncomes.length; i++) {
    const currentMonth = monthNumbers[i];
    const income = monthIncomes[i];

    // 断档判断
    const isBreak = lastMonth && getMonthDiff(lastMonth, currentMonth) > 1;

    if (isBreak) {
      // 保存当前组
      if (currentGroup.length > 0) {
        groups.push({
          groupIndex: groupIndex,
          title: getGroupTitle(groupStartMonth, lastMonth, groupIndex),
          data: [...currentGroup],
        });
        groupIndex++;
      }

      // 重置累计值
      cumulativeIncome = 0;
      cumulativeCost = 0;
      cumulativeFixedDeduct = 0;
      cumulativeTaxableIncome = 0;
      lastCumulativeTax = 0;
      continuousMonths = 0;
      currentGroup = [];
      groupStartMonth = currentMonth;
    }

    // 设置组的起始月份
    if (i === 0 || isBreak) {
      groupStartMonth = currentMonth;
    }

    // 新月份+1
    if (i === 0 || currentMonth !== lastMonth) {
      continuousMonths++;
    }

    cumulativeIncome += income;
    cumulativeCost = cumulativeIncome * COST_DEDUCTION_RATE;
    cumulativeFixedDeduct = MONTHLY_FIXED_DEDUCTION * continuousMonths;
    cumulativeTaxableIncome =
      cumulativeIncome - cumulativeCost - cumulativeFixedDeduct;

    // 如果累计应纳税所得额小于等于0，设为0
    if (cumulativeTaxableIncome <= 0) cumulativeTaxableIncome = 0;

    const level = getTaxRateLevel(cumulativeTaxableIncome);
    let cumulativeTax =
      cumulativeTaxableIncome * level.rate - level.quickDeduction;

    // 如果累计应缴个税小于等于0，设为0
    if (cumulativeTax <= 0) cumulativeTax = 0;

    let currentTax = cumulativeTax - lastCumulativeTax;

    // 如果本期应缴个税小于等于0，设为0
    if (currentTax <= 0) currentTax = 0;

    currentGroup.push({
      month: currentMonth,
      income: Math.max(0, income),
      cumulativeIncome: Math.max(0, cumulativeIncome),
      cumulativeCost: Math.max(0, cumulativeCost),
      cumulativeFixedDeduct: Math.max(0, cumulativeFixedDeduct),
      cumulativeTaxableIncome: Math.max(0, cumulativeTaxableIncome),
      rate: level.rate,
      quickDeduction: level.quickDeduction,
      cumulativeTax: Math.max(0, cumulativeTax),
      lastCumulativeTax: Math.max(0, lastCumulativeTax),
      currentTax: Math.max(0, currentTax),
    });

    lastCumulativeTax = cumulativeTax;
    lastMonth = currentMonth;
  }

  // 保存最后一组
  if (currentGroup.length > 0) {
    groups.push({
      groupIndex: groupIndex,
      title: getGroupTitle(groupStartMonth, lastMonth, groupIndex),
      data: currentGroup,
    });
  }

  return groups;
}

// 工具函数：计算两个yyyy-MM字符串的月份差
function getMonthDiff(month1: string, month2: string): number {
  // 如果是"第XX月"格式
  if (month1.includes("第") && month1.includes("月")) {
    const m1 = parseInt(month1.replace("第", "").replace("月", ""));
    const m2 = parseInt(month2.replace("第", "").replace("月", ""));
    return m2 - m1;
  }

  // 如果是"项目名-第XX月"格式
  if (month1.includes("-第") && month1.includes("月")) {
    const m1 = parseInt(month1.split("-第")[1].replace("月", ""));
    const m2 = parseInt(month2.split("-第")[1].replace("月", ""));
    return m2 - m1;
  }

  // 如果是"yyyy-MM"格式
  if (month1.includes("-") && month2.includes("-")) {
    const [y1, m1] = month1.split("-").map(Number);
    const [y2, m2] = month2.split("-").map(Number);
    return (y2 - y1) * 12 + (m2 - m1);
  }

  return 0;
}

// 按项目分组计算税收
export function calculateTaxByProjects(
  projectsData: ProjectIncomeData[]
): ProjectTaxCalculationGroup[] {
  const result: ProjectTaxCalculationGroup[] = [];

  for (const project of projectsData) {
    if (project.monthIncomes.length === 0) continue;

    // 为每个项目计算分组
    const projectGroups = calculateTaxByMonthsGrouped(
      project.monthIncomes,
      project.monthNumbers
    );

    // 计算项目总计
    const projectTotalIncome = project.monthIncomes.reduce(
      (sum, income) => sum + income,
      0
    );
    const projectTotalTax = projectGroups
      .flatMap((group) => group.data)
      .reduce((sum, item) => sum + Number(item.currentTax), 0);

    result.push({
      projectId: project.projectId,
      projectName: project.projectName,
      groups: projectGroups,
      projectTotalIncome,
      projectTotalTax,
    });
  }

  return result;
}

// 生成组标题 - 支持项目前缀
function getGroupTitle(
  startMonth: string,
  endMonth: string | null,
  groupIndex: number
): string {
  // 提取项目名称
  const projectName = startMonth.includes("-") ? startMonth.split("-")[0] : "";
  const startMonthOnly = startMonth.includes("-")
    ? startMonth.split("-")[1]
    : startMonth;
  const endMonthOnly =
    endMonth && endMonth.includes("-") ? endMonth.split("-")[1] : endMonth;

  if (!endMonthOnly || startMonthOnly === endMonthOnly) {
    return projectName
      ? `${projectName} - 计算组 ${groupIndex + 1}：${startMonthOnly}`
      : `计算组 ${groupIndex + 1}：${startMonthOnly}`;
  }

  return projectName
    ? `${projectName} - 计算组 ${
        groupIndex + 1
      }：${startMonthOnly} 至 ${endMonthOnly}`
    : `计算组 ${groupIndex + 1}：${startMonthOnly} 至 ${endMonthOnly}`;
}

// 合并所有项目的同月份收入并计算税收
export function calculateTaxByProjectsMerged(
  projectsData: ProjectIncomeData[]
): TaxCalculationGroup[] {
  // 合并所有项目的同月份收入
  const mergedMonthData: Map<string, number> = new Map();

  for (const project of projectsData) {
    for (let i = 0; i < project.monthNumbers.length; i++) {
      const monthKey = project.monthNumbers[i];
      const income = project.monthIncomes[i];

      if (mergedMonthData.has(monthKey)) {
        mergedMonthData.set(monthKey, mergedMonthData.get(monthKey)! + income);
      } else {
        mergedMonthData.set(monthKey, income);
      }
    }
  }

  // 转换为数组并排序
  const sortedEntries = Array.from(mergedMonthData.entries()).sort((a, b) => {
    // 提取月份数字进行排序
    const monthA = parseInt(a[0].replace("第", "").replace("月", ""));
    const monthB = parseInt(b[0].replace("第", "").replace("月", ""));
    return monthA - monthB;
  });

  const monthIncomes = sortedEntries.map((entry) => entry[1]);
  const monthNumbers = sortedEntries.map((entry) => entry[0]);

  return calculateTaxByMonthsGrouped(monthIncomes, monthNumbers);
}

// 计算项目分组和合并结果
export function calculateTaxWithMergedResults(
  projectsData: ProjectIncomeData[]
): {
  projectResults: ProjectTaxCalculationGroup[];
  mergedResults: TaxCalculationGroup[];
} {
  const projectResults = calculateTaxByProjects(projectsData);
  // 恢复调用已修正的合并计算函数
  const mergedResults = calculateTaxByProjectsMerged(projectsData);

  return {
    projectResults,
    mergedResults,
  };
}

// 导出类型
export type {
  TaxCalculationResult,
  TaxCalculationGroup,
  ProjectTaxCalculationGroup,
  ProjectIncomeData,
};
