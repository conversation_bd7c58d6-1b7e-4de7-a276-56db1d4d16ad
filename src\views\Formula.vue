<script setup lang="ts">
import { useRouter } from "vue-router";
import {
  mdiArrowLeft,
  mdiBookOpenPageVariantOutline,
  mdiTable,
  mdiHelpCircleOutline,
  mdiCalendarMultiselect,
  mdiCalendarMonth,
  mdiInformationOutline,
  mdiFileDocumentOutline,
  mdiCommentQuestionOutline,
  mdiGavel,
} from "@mdi/js";

const router = useRouter();

const goBack = () => {
  router.push("/");
};

// 税率表数据
const taxRateData = [
  { level: 1, range: "不超过36000元", rate: 3, deduction: 0 },
  { level: 2, range: "超过36000元至144000元的部分", rate: 10, deduction: 2520 },
  {
    level: 3,
    range: "超过144000元至300000元的部分",
    rate: 20,
    deduction: 16920,
  },
  {
    level: 4,
    range: "超过300000元至420000元的部分",
    rate: 25,
    deduction: 31920,
  },
  {
    level: 5,
    range: "超过420000元至660000元的部分",
    rate: 30,
    deduction: 52920,
  },
  {
    level: 6,
    range: "超过660000元至960000元的部分",
    rate: 35,
    deduction: 85920,
  },
  { level: 7, range: "超过960000元的部分", rate: 45, deduction: 181920 },
];

const headers = [
  { title: "级数", key: "level", align: "center" as const, sortable: false },
  {
    title: "累计预扣预缴应纳税所得额",
    key: "range",
    align: "center" as const,
    sortable: false,
  },
  {
    title: "预扣率（%）",
    key: "rate",
    align: "center" as const,
    sortable: false,
  },
  {
    title: "速算扣除数",
    key: "deduction",
    align: "center" as const,
    sortable: false,
  },
];

// 法规数据
const regulations = [
  {
    group: "税务规范性文件（国家税务总局公告2025年第15号）",
    items: [
      {
        type: "document",
        title: "《国家税务总局关于互联网平台企业报送涉税信息有关事项的公告》",
        description: "规范互联网平台企业涉税信息报送的具体要求和操作流程",
        url: "https://fgk.chinatax.gov.cn/zcfgk/c100012/c5241477/content.html",
      },
      {
        type: "interpretation",
        title:
          "《国家税务总局关于互联网平台企业报送涉税信息有关事项的公告》的解读",
        description: "详细解读公告内容，帮助理解政策要点和实施细节",
        url: "https://fgk.chinatax.gov.cn/zcfgk/c100015/c5241480/content.html",
      },
    ],
  },
  {
    group: "税务规范性文件（国家税务总局公告2025年第16号）",
    items: [
      {
        type: "document",
        title:
          "《国家税务总局关于互联网平台企业为平台内从业人员办理扣缴申报、代办申报若干事项的公告》",
        description: "明确平台企业为从业人员办理税务申报的相关规定",
        url: "https://fgk.chinatax.gov.cn/zcfgk/c100012/c5241472/content.html",
      },
      {
        type: "interpretation",
        title:
          "关于《国家税务总局关于互联网平台企业为平台内从业人员办理扣缴申报、代办申报若干事项的公告》的解读",
        description: "解读平台企业税务申报代办相关政策的具体操作要求",
        url: "https://fgk.chinatax.gov.cn/zcfgk/c100015/c5241475/content.html",
      },
    ],
  },
  {
    group: "互联网平台企业涉税信息报送规定（国务院第810号）",
    items: [
      {
        type: "document",
        title: "《互联网平台企业涉税信息报送规定（国务院第810号）》",
        description: "国务院关于印发810号文件的通知",
        url: "https://www.gov.cn/zhengce/zhengceku/202506/content_7029053.htm",
      },
    ],
  },
];
</script>

<template>
  <v-container>
    <!-- 页面标题 -->
    <v-row class="mb-6">
      <v-col cols="12">
        <div class="d-flex align-center">
          <v-btn icon variant="text" @click="goBack" class="me-3">
            <v-icon :icon="mdiArrowLeft" />
          </v-btn>
          <h1 class="text-h5 font-weight-bold text-primary">
            个税计算公式说明
          </h1>
        </div>
      </v-col>
    </v-row>

    <!-- 基本概念说明 -->
    <v-card variant="outlined" class="mb-6 border-thin">
      <v-card-title class="card-title">
        <v-icon :icon="mdiBookOpenPageVariantOutline" class="me-3" />
        一、基本概念
      </v-card-title>
      <v-divider />
      <v-list lines="two" class="py-0">
        <v-list-item>
          <v-list-item-title class="font-weight-bold">
            本期应预扣预缴税额
          </v-list-item-title>
          <v-list-item-subtitle class="formula-text">
            （累计收入 - 累计免税收入 - 累计减除费用 - 累计专项扣除 -
            累计专项附加扣除 - 累计依法确定的其他扣除）× 预扣率 - 速算扣除数 -
            累计减免税额 - 累计已预扣预缴税额
          </v-list-item-subtitle>
        </v-list-item>

        <v-divider />

        <v-list-item>
          <v-list-item-title class="font-weight-bold"
            >累计费用</v-list-item-title
          >
          <v-list-item-subtitle class="formula-text">
            累计收入 × 20%
          </v-list-item-subtitle>
        </v-list-item>

        <v-divider />

        <v-list-item>
          <v-list-item-title class="font-weight-bold">
            累计减除费用
          </v-list-item-title>
          <v-list-item-subtitle class="formula-text">
            5000元/月 × 纳税人当年截至本月在本单位的连续任职受雇月份数
          </v-list-item-subtitle>
        </v-list-item>
      </v-list>
    </v-card>

    <!-- 个人所得税预扣率表 -->
    <v-card variant="outlined" class="mb-6 border-thin">
      <v-card-title class="card-title">
        <v-icon :icon="mdiTable" class="me-3" />
        二、个人所得税预扣率表
      </v-card-title>
      <v-card-subtitle>
        （居民个人工资、薪金所得预扣预缴适用）
      </v-card-subtitle>
      <v-divider class="mt-2" />
      <v-data-table
        :headers="headers"
        :items="taxRateData"
        hide-default-footer
        class="tax-rate-table"
        density="compact"
      >
        <template #item.level="{ item }">
          <v-chip color="primary" size="small" variant="flat">
            {{ item.level }}
          </v-chip>
        </template>
        <template #item.rate="{ item }">
          <v-chip color="success" size="small" variant="flat">
            {{ item.rate }}%
          </v-chip>
        </template>
        <template #item.deduction="{ item }">
          <span class="font-weight-bold">
            {{ item.deduction.toLocaleString() }}
          </span>
        </template>
      </v-data-table>
      <v-divider />
      <v-card-text class="text-right text-caption text-medium-emphasis">
        依据：总局公告2018年第61号
      </v-card-text>
    </v-card>

    <!-- 使用说明 -->
    <v-card variant="outlined" class="mb-6 border-thin">
      <v-card-title class="card-title">
        <v-icon :icon="mdiHelpCircleOutline" class="me-3" />
        三、使用说明
      </v-card-title>
      <v-divider />
      <v-list lines="two" class="py-0">
        <v-list-item :prepend-icon="mdiCalendarMultiselect">
          <v-list-item-title>按月累计收入计算</v-list-item-title>
          <v-list-item-subtitle>
            适用于每月收入不同或收入月份不连续的情况，系统会自动检测断档并分组计算。
          </v-list-item-subtitle>
        </v-list-item>
        <v-divider />
        <v-list-item :prepend-icon="mdiCalendarMonth">
          <v-list-item-title>按月均收入计算</v-list-item-title>
          <v-list-item-subtitle>
            适用于连续每个月收入全额一样的情况，输入月均收入和连续月数即可。
          </v-list-item-subtitle>
        </v-list-item>
        <v-divider />
        <v-list-item :prepend-icon="mdiInformationOutline">
          <v-list-item-title>计算结果说明</v-list-item-title>
          <v-list-item-subtitle>
            本计算器结果仅供参考，实际纳税请以税务部门核算为准。
          </v-list-item-subtitle>
        </v-list-item>
      </v-list>
    </v-card>

    <!-- 相关政策法规 -->
    <v-card variant="outlined" class="border-thin">
      <v-card-title class="card-title">
        <v-icon :icon="mdiGavel" class="me-3" />
        四、相关政策法规
      </v-card-title>
      <v-card-subtitle class="pb-0"> 最新税务政策与法规解读 </v-card-subtitle>
      <v-divider class="mt-3" />

      <v-card-text class="pa-0">
        <v-expansion-panels flat multiple variant="accordion">
          <v-expansion-panel
            v-for="(regulation, index) in regulations"
            :key="index"
            :value="index"
          >
            <v-expansion-panel-title class="regulation-panel-title">
              <div class="d-flex align-center">
                <v-icon
                  :icon="mdiFileDocumentOutline"
                  class="me-3"
                  color="primary"
                />
                <div>
                  <div class="text-subtitle-1 font-weight-bold">
                    {{ regulation.group }}
                  </div>
                  <div class="text-caption text-medium-emphasis">
                    {{ regulation.items.length }} 个相关文件
                  </div>
                </div>
              </div>
            </v-expansion-panel-title>

            <v-expansion-panel-text>
              <v-list lines="three" class="py-0">
                <template
                  v-for="(item, itemIndex) in regulation.items"
                  :key="itemIndex"
                >
                  <v-list-item
                    :href="item.url"
                    target="_blank"
                    rel="noopener"
                    class="mb-2 rounded-lg regulation-item"
                    elevation="0"
                  >
                    <template #prepend>
                      <v-avatar
                        :color="item.type === 'document' ? 'primary' : 'info'"
                        size="40"
                        class="me-4"
                      >
                        <v-icon
                          :icon="
                            item.type === 'document'
                              ? mdiFileDocumentOutline
                              : mdiCommentQuestionOutline
                          "
                          color="white"
                          size="20"
                        />
                      </v-avatar>
                    </template>

                    <v-list-item-title
                      class="font-weight-medium text-wrap mb-1"
                    >
                      {{ item.title }}
                    </v-list-item-title>
                    <v-list-item-subtitle class="text-wrap mb-2">
                      {{ item.description }}
                    </v-list-item-subtitle>
                    <v-list-item-subtitle class="d-flex align-center">
                      <v-chip
                        :color="item.type === 'document' ? 'primary' : 'info'"
                        size="x-small"
                        variant="flat"
                        class="me-2"
                      >
                        {{ item.type === "document" ? "公告原文" : "政策解读" }}
                      </v-chip>
                      <span class="text-caption">点击查看详情</span>
                      <v-spacer />
                      <v-icon
                        icon="mdi-open-in-new"
                        size="small"
                        class="text-medium-emphasis"
                      />
                    </v-list-item-subtitle>
                  </v-list-item>
                  <v-divider
                    v-if="itemIndex < regulation.items.length - 1"
                    class="my-2"
                  />
                </template>
              </v-list>
            </v-expansion-panel-text>
          </v-expansion-panel>
        </v-expansion-panels>
      </v-card-text>

      <v-divider />
      <v-card-text class="text-center">
        <v-chip variant="tonal" color="warning" size="small" class="me-2">
          重要提醒
        </v-chip>
        <p class="text-body-2 text-medium-emphasis mt-2 mb-0">
          以上法规仅供参考，请以国家税务总局官方发布的最新政策为准。
          如有疑问，建议咨询当地税务机关。
        </p>
      </v-card-text>
    </v-card>
  </v-container>
</template>

<style scoped>
.card-title {
  font-weight: 600;
  display: flex;
  align-items: center;
}

.formula-text {
  white-space: normal;
  line-height: 1.6;
  color: #616161;
}

.tax-rate-table :deep(.v-data-table__wrapper) {
  overflow-x: auto;
}

.v-theme--dark .formula-text {
  color: #bdbdbd;
}

/* 法规面板样式 */
.regulation-panel-title {
  background: linear-gradient(
    90deg,
    rgba(var(--v-theme-primary), 0.05) 0%,
    transparent 100%
  );
}

/* 法规列表项样式 */
.regulation-item {
  transition: all 0.2s ease-in-out;
  border: 1px solid transparent;
}

.regulation-item:hover {
  background-color: rgba(var(--v-theme-primary), 0.04) !important;
  border-color: rgba(var(--v-theme-primary), 0.2);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.v-theme--dark .regulation-item:hover {
  background-color: rgba(var(--v-theme-primary), 0.08) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* 展开面板标题悬停效果 */
.regulation-panel-title:hover {
  background: linear-gradient(
    90deg,
    rgba(var(--v-theme-primary), 0.08) 0%,
    transparent 100%
  );
}

/* 头像动画 */
.regulation-item .v-avatar {
  transition: transform 0.2s ease;
}

.regulation-item:hover .v-avatar {
  transform: scale(1.05);
}

/* 移动端优化 */
@media (max-width: 960px) {
  .regulation-item {
    margin-bottom: 8px !important;
  }

  .regulation-item .v-avatar {
    width: 36px !important;
    height: 36px !important;
  }

  .regulation-item:hover {
    transform: none !important;
  }

  .regulation-item:hover .v-avatar {
    transform: none !important;
  }
}
</style>
