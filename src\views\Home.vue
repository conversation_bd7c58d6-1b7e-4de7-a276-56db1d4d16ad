<script setup lang="ts">
import { ref, defineAsyncComponent } from "vue";
import { useRouter } from "vue-router";
import {
  mdiCalculator,
  mdiHelpCircle,
  mdiCalendarMultiselect,
  mdiCalendarMonth,
  mdiFileExcel,
} from "@mdi/js";

const MonthlyAverageIncomeCalculator = defineAsyncComponent(
  () => import("../components/monthly-average-income-calculator/index.vue")
);
const AnyMonthlyAverageIncomeCalculator = defineAsyncComponent(
  () => import("../components/any-monthly-average-income-calculator/index.vue")
);
const ExcelBatchCalculator = defineAsyncComponent(
  () => import("../components/excel-batch-calculator/index.vue")
);

const activeTab = ref(0);

const router = useRouter();
const goToFormula = () => {
  router.push("/formula");
};
</script>

<template>
  <v-responsive :max-width="1600" class="mx-auto">
    <!-- 标题和说明 -->
    <v-row class="mb-4">
      <v-col cols="12">
        <v-card class="pa-4" variant="tonal">
          <v-card-title class="text-h5 text-center">
            <v-icon :icon="mdiCalculator" class="me-2" />
            个人所得税计算器
          </v-card-title>
          <v-card-text class="text-center">
            <p class="text-body-1 mb-2">
              基于最新个人所得税法，支持多项目分别计算和合并计算
            </p>
            <v-btn
              variant="outlined"
              color="primary"
              size="small"
              @click="goToFormula"
              :prepend-icon="mdiHelpCircle"
            >
              计算公式说明
            </v-btn>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- 计算器标签页 -->
    <v-card>
      <v-tabs v-model="activeTab" color="primary" align-tabs="center" grow>
        <v-tab :value="0">
          <v-icon :icon="mdiCalendarMultiselect" class="me-2" />
          按月累计收入计算
        </v-tab>
        <v-tab :value="1">
          <v-icon :icon="mdiCalendarMonth" class="me-2" />
          按月均收入计算
        </v-tab>
        <v-tab :value="2">
          <v-icon :icon="mdiFileExcel" class="me-2" />
          Excel批量计算
        </v-tab>
      </v-tabs>

      <v-tabs-window v-model="activeTab">
        <v-tabs-window-item :value="0">
          <div class="pa-4">
            <AnyMonthlyAverageIncomeCalculator />
          </div>
        </v-tabs-window-item>

        <v-tabs-window-item :value="1">
          <div class="pa-4">
            <MonthlyAverageIncomeCalculator />
          </div>
        </v-tabs-window-item>

        <v-tabs-window-item :value="2">
          <div class="pa-4">
            <ExcelBatchCalculator />
          </div>
        </v-tabs-window-item>
      </v-tabs-window>
    </v-card>
  </v-responsive>
</template>

<style scoped>
/* ... existing styles ... */
</style>
